import { useState, useEffect } from "react";
import { safeIpcInvoke } from "../utils/electron";
import { ReportModal } from "../components/ReportModal";
import { Modal } from "../components/modal";
import { Button } from "../components/button";
import { MinimalLoader } from "../components/LoadingSpinner";
import { SessionStatusBadge } from "../components/SessionStatusIndicator";
import { useMasterDataAccess } from "../hooks/useRoleAccess";
import { RoleBasedComponent } from "../components/RoleBasedComponent";
import { TransactionAdjustmentModal } from "../components/TransactionAdjustmentModal";
import DisputeFormPDF, { DisputeFormDocument, type DisputeFormData } from "../components/DisputeFormPDF";
import { pdf } from '@react-pdf/renderer';
import * as XLSX from "xlsx";
import {
  showErrorDialog,
  showWarningDialog,
  showInfoDialog,
  showSuccessNotification
} from "../utils/systemNotifications";

interface Transaction {
  id: number;
  reference_no?: string;
  transaction_id: string;
  transaction_app_id: string;
  transaction_out_id?: string;
  transaction_card_no?: string;
  transaction_merchant_id?: string;
  transaction_merchant_name?: string;
  transaction_merchant_vat?: string;
  transaction_time: string;
  transaction_amount: number;
  transaction_refund_id?: string;
  transaction_refund_out_id?: string;
  transaction_mch_id?: string;
  transaction_sub_mch_id?: string;
  transaction_trade_type?: string;
  transaction_trade_status?: string;
  transaction_bank_type?: string;
  transaction_fee_type?: string;
  transaction_coupon_amount?: number;
  transaction_file_name: string;
  transaction_file_name_backup?: string;
  transaction_channel_type?: string;
  create_by: string;
  create_dt: string;
  update_by?: string;
  update_dt?: string;
}

interface TransactionResponse {
  success: boolean;
  message: string;
  data?: Transaction | Transaction[];
  pagination?: {
    currentPage: number;
    totalPages: number;
    totalRecords: number;
    pageSize: number;
    hasNextPage: boolean;
    hasPreviousPage: boolean;
  };
  error?: string;
}

interface PaginationParams {
  page?: number;
  pageSize?: number;
  search?: string;
  sortBy?: string;
  sortOrder?: "ASC" | "DESC";
  startDate?: string;
  endDate?: string;
  merchantVat?: string;
  merchantName?: string;
  referenceNo?: string;
  cardNo?: string;
  amount?: number;
  subMchId?: string;
  status?: string;
  channel?: string;
}

export function TransactionListScreen() {
  const { canExport } = useMasterDataAccess();
  const [transactions, setTransactions] = useState<Transaction[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [viewingTransaction, setViewingTransaction] =
    useState<Transaction | null>(null);
  const [isReportModalOpen, setIsReportModalOpen] = useState(false);
  const [isPDFModalOpen, setIsPDFModalOpen] = useState(false);
  const [isAdjustmentModalOpen, setIsAdjustmentModalOpen] = useState(false);
  const [adjustmentTransaction, setAdjustmentTransaction] = useState<Transaction | null>(null);
  const [isNewAdjustmentModalOpen, setIsNewAdjustmentModalOpen] = useState(false);
  const [newAdjustmentTransaction, setNewAdjustmentTransaction] = useState<Transaction | null>(null);

  // Pagination state
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(0);
  const [totalRecords, setTotalRecords] = useState(0);
  const [pageSize, setPageSize] = useState(() => {
    const saved = localStorage.getItem("transactionList_pageSize");
    return saved ? parseInt(saved) : 10;
  });

  // Helper function to get default date range (1 week ago to today)
  const getDefaultDateRange = () => {
    const today = new Date();
    const oneWeekAgo = new Date();
    oneWeekAgo.setDate(today.getDate() - 30);
    
    return {
      startDate: oneWeekAgo.toISOString().split('T')[0], // Format: YYYY-MM-DD
      endDate: today.toISOString().split('T')[0] // Format: YYYY-MM-DD
    };
  };

  // Search and filter state (form values - not applied until "Apply Filter" is clicked)
  const [searchTerm, setSearchTerm] = useState("");
  const [startDate, setStartDate] = useState(() => getDefaultDateRange().startDate);
  const [endDate, setEndDate] = useState(() => getDefaultDateRange().endDate);
  const [merchantVat, setMerchantVat] = useState("");
  const [merchantName, setMerchantName] = useState("");
  const [referenceNo, setReferenceNo] = useState("");
  const [cardNo, setCardNo] = useState("");
  const [amount, setAmount] = useState("");
  const [subMchId, setSubMchId] = useState("");
  const [status, setStatus] = useState("");
  const [channel, setChannel] = useState("");

  // Applied filter state (actual filters being used for API calls)
  const [appliedFilters, setAppliedFilters] = useState<PaginationParams>({});
  const [sortBy, setSortBy] = useState("transaction_time");
  const [sortOrder, setSortOrder] = useState<"ASC" | "DESC">("DESC");
  const [isExporting, setIsExporting] = useState(false);

  // Reload transactions when sorting changes (only if we have applied filters)
  useEffect(() => {
    // Only reload if we have filters applied and we're on page 1
    if (Object.keys(appliedFilters).length > 0 && currentPage === 1) {
      loadTransactions(1, pageSize, appliedFilters);
    }
  }, [sortBy, sortOrder, appliedFilters]); // Include appliedFilters as dependency

  const loadTransactions = async (
    page: number = currentPage,
    pageSizeParam: number = pageSize,
    appliedFiltersParam?: PaginationParams
  ) => {
    setIsLoading(true);
    console.log("appliedFiltersParam", appliedFiltersParam);
    try {
      // If no filters parameter is provided, don't load data automatically
      if (
        appliedFiltersParam === undefined ||
        Object.keys(appliedFiltersParam).length === 0
      ) {
        setTransactions([]);
        setCurrentPage(1);
        setTotalPages(0);
        setTotalRecords(0);
        setIsLoading(false);
        return;
      }

      // Use the provided filters (could be empty object {} for all data)
      const filtersToUse = appliedFiltersParam;

      const params: PaginationParams = {
        page,
        pageSize: pageSizeParam,
        sortBy,
        sortOrder,
        ...filtersToUse, // Use applied filters (empty object loads all data)
      };

      const response: TransactionResponse = await safeIpcInvoke(
        "get-transactions-paginated",
        params
      );

      if (response.success && response.data && response.pagination) {
        setTransactions(
          Array.isArray(response.data) ? response.data : [response.data]
        );
        setCurrentPage(response.pagination.currentPage);
        setTotalPages(response.pagination.totalPages);
        setTotalRecords(response.pagination.totalRecords);
        console.log(
          `✅ Loaded ${
            Array.isArray(response.data) ? response.data.length : 1
          } transactions`
        );
      } else {
        console.error("❌ Failed to load transactions:", response.message);
        setTransactions([]);
        setTotalPages(0);
        setTotalRecords(0);
      }
    } catch (error) {
      console.error("❌ Error loading transactions:", error);
      setTransactions([]);
      setTotalPages(0);
      setTotalRecords(0);
    } finally {
      setIsLoading(false);
    }
  };

  const handleView = (transaction: Transaction) => {
    setViewingTransaction(transaction);
    setIsModalOpen(true);
  };

  const handleCloseModal = () => {
    setIsModalOpen(false);
    setViewingTransaction(null);
  };

  const handleAdjustmentRequest = (transaction: Transaction) => {
    setAdjustmentTransaction(transaction);
    setIsAdjustmentModalOpen(true);
  };

  const handleCloseAdjustmentModal = () => {
    setIsAdjustmentModalOpen(false);
    setAdjustmentTransaction(null);
  };

  const handleNewAdjustmentRequest = (transaction: Transaction) => {
    setNewAdjustmentTransaction(transaction);
    setIsNewAdjustmentModalOpen(true);
  };

  const handleCloseNewAdjustmentModal = () => {
    setIsNewAdjustmentModalOpen(false);
    setNewAdjustmentTransaction(null);
  };

  const handleAdjustmentSuccess = () => {
    // Reload transactions to show updated data
    loadTransactions(currentPage, pageSize, appliedFilters);
  };

  const handlePrintAdjustmentForm = async () => {
    if (!adjustmentTransaction) return;

    try {
      // Convert transaction data to dispute form data
      const disputeData: DisputeFormData = {
        requestDate: new Date().toLocaleDateString('th-TH', {
          day: '2-digit',
          month: '2-digit',
          year: '2-digit'
        }),
        requestId: adjustmentTransaction.id.toString(),
        institutionName: "E-POS Service Company Limited",
        iin: "30340764",
        contactName: "Admin User",
        phone: "(662) 8215459",
        fax: "",
        email: "<EMAIL>",
        title: "Mr.",
        cardNumber: adjustmentTransaction.transaction_card_no || "",
        transactionDate: new Date(adjustmentTransaction.transaction_time).toLocaleDateString('th-TH'),
        transactionTime: new Date(adjustmentTransaction.transaction_time).toLocaleTimeString('th-TH'),
        merchantId: adjustmentTransaction.transaction_merchant_id || "",
        transactionAmount: adjustmentTransaction.transaction_amount?.toLocaleString('th-TH', { minimumFractionDigits: 2 }) || "0.00",
        transactionType: adjustmentTransaction.transaction_trade_type || "",
        referenceNumber: adjustmentTransaction.reference_no || "",
        terminalId: "33173516",
        currencyCode: "764",
        merchantName: adjustmentTransaction.transaction_merchant_name || "",
        disputedAmount: adjustmentTransaction.transaction_amount?.toLocaleString('th-TH', { minimumFractionDigits: 2 }) || "0.00",
        amountType: "full",
        disputeType: ["Credit Adjustment"],
        reasonCode: "",
        supportingDocs: "no",
        pages: "1",
        additionalMessage: "Refund diff amount to customer.",
      };

      // Generate PDF blob
      const blob = await pdf(<DisputeFormDocument data={disputeData} />).toBlob();

      // Create object URL
      const url = URL.createObjectURL(blob);

      // Open in new window for printing
      const printWindow = window.open(url, '_blank');

      if (printWindow) {
        printWindow.onload = () => {
          printWindow.print();
        };

        // Clean up URL after a short delay
        setTimeout(() => {
          URL.revokeObjectURL(url);
        }, 1000);

        showSuccessNotification('PDF opened for printing!');
      } else {
        throw new Error('Unable to open print window');
      }
    } catch (error) {
      console.error('Error printing PDF:', error);
      showErrorDialog('Failed to print PDF. Please try again.');
    }
  };

  const handleDownloadAdjustmentForm = async () => {
    if (!adjustmentTransaction) return;

    try {
      // Convert transaction data to dispute form data
      const disputeData: DisputeFormData = {
        requestDate: new Date().toLocaleDateString('th-TH', {
          day: '2-digit',
          month: '2-digit',
          year: '2-digit'
        }),
        requestId: adjustmentTransaction.id.toString(),
        institutionName: "E-POS Service Company Limited",
        iin: "30340764",
        contactName: "Admin User",
        phone: "(662) 8215459",
        fax: "",
        email: "<EMAIL>",
        title: "Mr.",
        cardNumber: adjustmentTransaction.transaction_card_no || "",
        transactionDate: new Date(adjustmentTransaction.transaction_time).toLocaleDateString('th-TH'),
        transactionTime: new Date(adjustmentTransaction.transaction_time).toLocaleTimeString('th-TH'),
        merchantId: adjustmentTransaction.transaction_merchant_id || "",
        transactionAmount: adjustmentTransaction.transaction_amount?.toLocaleString('th-TH', { minimumFractionDigits: 2 }) || "0.00",
        transactionType: adjustmentTransaction.transaction_trade_type || "",
        referenceNumber: adjustmentTransaction.reference_no || "",
        terminalId: "33173516",
        currencyCode: "764",
        merchantName: adjustmentTransaction.transaction_merchant_name || "",
        disputedAmount: adjustmentTransaction.transaction_amount?.toLocaleString('th-TH', { minimumFractionDigits: 2 }) || "0.00",
        amountType: "full",
        disputeType: ["Credit Adjustment"],
        reasonCode: "",
        supportingDocs: "no",
        pages: "1",
        additionalMessage: "Refund diff amount to customer.",
      };

      // Generate PDF blob
      const blob = await pdf(<DisputeFormDocument data={disputeData} />).toBlob();

      // Create download link
      const url = URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `adjustment-form-${adjustmentTransaction.id}-${Date.now()}.pdf`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);

      // Clean up URL
      setTimeout(() => {
        URL.revokeObjectURL(url);
      }, 1000);

      showSuccessNotification('PDF downloaded successfully!');
    } catch (error) {
      console.error('Error downloading PDF:', error);
      showErrorDialog('Failed to download PDF. Please try again.');
    }
  };

  // Pagination functions
  const handlePageChange = (newPage: number) => {
    if (newPage >= 1 && newPage <= totalPages) {
      setCurrentPage(newPage);
      loadTransactions(newPage, pageSize, appliedFilters);
    }
  };

  const handlePageSizeChange = (newPageSize: number) => {
    setPageSize(newPageSize);
    localStorage.setItem("transactionList_pageSize", newPageSize.toString());
    setCurrentPage(1);
    loadTransactions(1, newPageSize, appliedFilters);
  };

  const handleSort = (column: string) => {
    const newSortOrder =
      sortBy === column && sortOrder === "ASC" ? "DESC" : "ASC";

    // Update sort state
    setSortBy(column);
    setSortOrder(newSortOrder);

    // Reset to first page when sorting changes
    setCurrentPage(1);
  };

  // Apply filters - convert form values to applied filters and reload data
  const applyFilters = () => {
    const newFilters: PaginationParams = {};

    if (searchTerm.trim()) newFilters.search = searchTerm.trim();
    if (startDate) newFilters.startDate = startDate;
    if (endDate) newFilters.endDate = endDate;
    if (merchantVat.trim()) newFilters.merchantVat = merchantVat.trim();
    if (merchantName.trim()) newFilters.merchantName = merchantName.trim();
    if (referenceNo.trim()) newFilters.referenceNo = referenceNo.trim();
    if (cardNo.trim()) newFilters.cardNo = cardNo.trim();
    if (amount.trim()) newFilters.amount = parseFloat(amount.trim());
    if (subMchId.trim()) newFilters.subMchId = subMchId.trim();
    if (status) newFilters.status = status;
    if (channel) newFilters.channel = channel;

    setAppliedFilters(newFilters);
    setCurrentPage(1);
    // Call loadTransactions immediately with the new filters
    loadTransactions(1, pageSize, newFilters);
  };
  console.log("newFilters", appliedFilters);
  // Clear all filters
  const clearFilters = () => {
    const defaultDates = getDefaultDateRange();
    
    // Clear form values but keep default date range
    setSearchTerm("");
    setStartDate(defaultDates.startDate);
    setEndDate(defaultDates.endDate);
    setMerchantVat("");
    setMerchantName("");
    setReferenceNo("");
    setCardNo("");
    setAmount("");
    setSubMchId("");
    setStatus("");
    setChannel("");

    // Clear applied filters and reload
    setAppliedFilters({});
    setCurrentPage(1);
    // Call loadTransactions immediately with empty filters
    loadTransactions(1, pageSize, {});
  };

  // Export to Excel function
  const exportToExcel = async () => {
    if (!canExport) {
      await showErrorDialog(
        "You don't have permission to export transaction data",
        "Access Denied",
        "Please contact your administrator for export permissions."
      );
      return;
    }

    if (Object.keys(appliedFilters).length === 0) {
      await showWarningDialog(
        "Please apply filters first before exporting.",
        "No Filters Applied",
        "Use the filter options above to select the data you want to export."
      );
      return;
    }

    setIsExporting(true);
    try {
      console.log("📊 Starting Excel export with filters:", appliedFilters);

      // Get all transactions with current filters (no pagination limit)
      const params: PaginationParams = {
        page: 1,
        pageSize: 10000, // Large number to get all filtered results
        sortBy,
        sortOrder,
        ...appliedFilters,
      };

      const response: TransactionResponse = await safeIpcInvoke(
        "get-transactions-paginated",
        params
      );

      if (response.success && response.data) {
        const allTransactions = Array.isArray(response.data)
          ? response.data
          : [response.data];

        if (allTransactions.length === 0) {
          await showInfoDialog(
            "No transactions found to export.",
            "No Data",
            "Try adjusting your filter criteria to include more data."
          );
          return;
        }

        // Prepare data for Excel export
        const exportData = allTransactions.map((transaction, index) => ({
          "No.": index + 1,
          "Transaction Time": formatDate(transaction.transaction_time),
          "Transaction ID": transaction.transaction_id,
          "Reference No": transaction.reference_no || "N/A",
          "Merchant Name": transaction.transaction_merchant_name || "N/A",
          "Merchant VAT": transaction.transaction_merchant_vat || "N/A",
          "Sub Merchant ID (TID)": transaction.transaction_sub_mch_id || "N/A",
          "Amount (THB)": transaction.transaction_amount,
          Status: transaction.transaction_trade_status || "UNKNOWN",
          Channel: transaction.transaction_channel_type || "UNKNOWN",
          "Card Number": transaction.transaction_card_no || "N/A",
          "Trade Type": transaction.transaction_trade_type || "N/A",
          "Bank Type": transaction.transaction_bank_type || "N/A",
          "Fee Type": transaction.transaction_fee_type || "THB",
          "Coupon Amount": transaction.transaction_coupon_amount || 0,
          "Out ID": transaction.transaction_out_id || "N/A",
          "MCH ID": transaction.transaction_mch_id || "N/A",
          "Refund ID": transaction.transaction_refund_id || "N/A",
          "Refund Out ID": transaction.transaction_refund_out_id || "N/A",
          "File Name": transaction.transaction_file_name,
          "Created By": transaction.create_by,
          "Created Date": formatDate(transaction.create_dt),
          "Updated By": transaction.update_by || "N/A",
          "Updated Date": transaction.update_dt
            ? formatDate(transaction.update_dt)
            : "N/A",
        }));

        // Create workbook and worksheet
        const workbook = XLSX.utils.book_new();
        const worksheet = XLSX.utils.json_to_sheet(exportData);

        // Set column widths for better readability
        const columnWidths = [
          { wch: 5 }, // No.
          { wch: 20 }, // Transaction Time
          { wch: 25 }, // Transaction ID
          { wch: 15 }, // Reference No
          { wch: 25 }, // Merchant Name
          { wch: 15 }, // Merchant VAT
          { wch: 15 }, // Sub Merchant ID
          { wch: 15 }, // Amount
          { wch: 12 }, // Status
          { wch: 12 }, // Channel
          { wch: 20 }, // Card Number
          { wch: 12 }, // Trade Type
          { wch: 12 }, // Bank Type
          { wch: 10 }, // Fee Type
          { wch: 15 }, // Coupon Amount
          { wch: 20 }, // Out ID
          { wch: 15 }, // MCH ID
          { wch: 20 }, // Refund ID
          { wch: 20 }, // Refund Out ID
          { wch: 25 }, // File Name
          { wch: 15 }, // Created By
          { wch: 20 }, // Created Date
          { wch: 15 }, // Updated By
          { wch: 20 }, // Updated Date
        ];
        worksheet["!cols"] = columnWidths;

        // Add worksheet to workbook
        XLSX.utils.book_append_sheet(workbook, worksheet, "Transactions");

        // Generate default filename with current date and filter info
        const now = new Date();
        const dateStr = now.toISOString().split("T")[0];
        const timeStr = now.toTimeString().split(" ")[0].replace(/:/g, "-");
        const filterInfo =
          Object.keys(appliedFilters).length > 0 ? "_filtered" : "_all";
        const defaultFilename = `transactions_export_${dateStr}_${timeStr}${filterInfo}.xlsx`;

        // Show save dialog
        const saveDialogResult = await safeIpcInvoke("show-save-dialog", {
          defaultPath: defaultFilename,
          filters: [
            { name: "Excel Files", extensions: ["xlsx"] },
            { name: "All Files", extensions: ["*"] },
          ],
        });

        if (
          saveDialogResult.success &&
          !saveDialogResult.canceled &&
          saveDialogResult.filePath
        ) {
          // Generate Excel buffer
          const excelBuffer = XLSX.write(workbook, {
            type: "buffer",
            bookType: "xlsx",
          });

          // Save file using IPC
          const saveResult = await safeIpcInvoke(
            "save-excel-file",
            saveDialogResult.filePath,
            excelBuffer
          );

          if (saveResult.success) {
            console.log(`✅ Excel export completed: ${saveResult.filePath}`);
            // Show success notification instead of alert
            showSuccessNotification(
              `Excel file exported successfully! ${allTransactions.length} records exported.`
            );
            // Also show detailed info dialog
            await showInfoDialog(
              `Excel file exported successfully!`,
              "Export Complete",
              `File: ${saveResult.filePath}\nRecords: ${allTransactions.length}`
            );
          } else {
            throw new Error(saveResult.error || "Failed to save Excel file");
          }
        } else if (saveDialogResult.canceled) {
          console.log("📝 Excel export canceled by user");
          return; // User canceled, don't show error
        } else {
          throw new Error(
            saveDialogResult.error || "Failed to show save dialog"
          );
        }
      } else {
        console.error(
          "❌ Failed to fetch transactions for export:",
          response.message
        );
        await showErrorDialog(
          "Failed to fetch transaction data for export. Please try again.",
          "Export Failed",
          `Error: ${response.message}`
        );
      }
    } catch (error) {
      console.error("❌ Error during Excel export:", error);
      await showErrorDialog(
        "An error occurred during export. Please try again.",
        "Export Error",
        error instanceof Error ? error.message : "Unknown error occurred"
      );
    } finally {
      setIsExporting(false);
    }
  };

  // Print Mock Report function
  const handlePrintMockReport = () => {
    setIsPDFModalOpen(true);
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    const hours = String(date.getHours()).padStart(2, '0');
    const minutes = String(date.getMinutes()).padStart(2, '0');
    const seconds = String(date.getSeconds()).padStart(2, '0');
    
    return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
  };

  const formatAmount = (amount: number) => {
    return new Intl.NumberFormat("th-TH", {
      style: "currency",
      currency: "THB",
      minimumFractionDigits: 2,
    }).format(amount);
  };

  const getStatusBadge = (status: string) => {
    // Normalize status to uppercase for consistent mapping
    const normalizedStatus = status.toUpperCase();

    const statusColors = {
      SUCCESS: "bg-green-100 text-green-800",
      REFUND: "bg-red-100 text-red-800",
      PENDING: "bg-yellow-100 text-yellow-800",
      FAILED: "bg-gray-100 text-gray-800",
    };

    const colorClass =
      statusColors[normalizedStatus as keyof typeof statusColors] ||
      "bg-gray-100 text-gray-800";

    return (
      <span
        className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${colorClass}`}
      >
        {normalizedStatus}
      </span>
    );
  };

  const getChannelBadge = (channel: string) => {
    const channelColors = {
      WeChat: "bg-green-100 text-green-800",
      UNIPAY: "bg-blue-100 text-blue-800",
      ALIPAY: "bg-orange-100 text-orange-800",
    };

    const colorClass =
      channelColors[channel as keyof typeof channelColors] ||
      "bg-gray-100 text-gray-800";

    return (
      <span
        className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${colorClass}`}
      >
        {channel}
      </span>
    );
  };

  return (
    <div className="flex flex-col gap-6 p-6 max-w-7xl mx-auto">
      {/* Header */}
      <div className="flex flex-col items-center mb-4">
        <div className="flex items-center gap-4 mb-2">
          <h1 className="text-3xl font-bold text-center">
            Transaction List
          </h1>
          {/* <SessionStatusBadge /> */}
        </div>
        <p className="text-gray-600 text-center">
          View and manage all transaction records with advanced filtering and
          pagination
        </p>
      </div>

      {/* Advanced Search and Filter Controls */}
      <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
        <h3 className="text-lg font-semibold mb-4 text-gray-900">
          Transaction Filters
        </h3>

        {/* Row 1: Basic Search and Date Range */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-4">
          {/* Search */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Search
            </label>
            <input
              type="text"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              placeholder="Transaction ID, Merchant..."
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>

          {/* Start Date */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Start Date
            </label>
            <input
              type="date"
              value={startDate}
              onChange={(e) => setStartDate(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>

          {/* End Date */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              End Date
            </label>
            <input
              type="date"
              value={endDate}
              onChange={(e) => setEndDate(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>
          {/* Channel Filter */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Channel
            </label>
            <select
              value={channel}
              onChange={(e) => setChannel(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="">All Channels</option>
              <option value="WeChat">WeChat</option>
              <option value="UNIPAY">UNIPAY</option>
              <option value="ALIPAY">Alipay</option>
            </select>
          </div>
        </div>

        {/* Row 2: Merchant Information */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-4">
          {/* Merchant VAT */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Merchant VAT
            </label>
            <input
              type="text"
              value={merchantVat}
              onChange={(e) => setMerchantVat(e.target.value)}
              placeholder="Merchant TID number"
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>

          {/* Merchant Name */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Merchant Name
            </label>
            <input
              type="text"
              value={merchantName}
              onChange={(e) => setMerchantName(e.target.value)}
              placeholder="Merchant business name"
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>

          {/* Reference Number */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Reference No
            </label>
            <input
              type="text"
              value={referenceNo}
              onChange={(e) => setReferenceNo(e.target.value)}
              placeholder="Reference number"
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>

          {/* Card Number */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Card Number
            </label>
            <input
              type="text"
              value={cardNo}
              onChange={(e) => setCardNo(e.target.value)}
              placeholder="Card number"
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>
        </div>

        {/* Row 3: Transaction Details */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-4">
          {/* Amount */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Amount
            </label>
            <input
              type="number"
              step="0.01"
              value={amount}
              onChange={(e) => setAmount(e.target.value)}
              placeholder="Transaction amount"
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>

          {/* Sub Merchant ID (TID) */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Sub Merchant ID (TID)
            </label>
            <input
              type="text"
              value={subMchId}
              onChange={(e) => setSubMchId(e.target.value)}
              placeholder="Terminal ID"
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>

          {/* Status Filter */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Status
            </label>
            <select
              value={status}
              onChange={(e) => setStatus(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="">All Statuses</option>
              <option value="success">Success</option>
              <option value="refund">Refund</option>
              <option value="PENDING">Pending</option>
              <option value="FAILED">Failed</option>
            </select>
          </div>
        </div>

        {/* Action Buttons */}
        <div className="flex flex-col sm:flex-row gap-4">
          <div className="flex gap-2">
            <Button onClick={clearFilters} variant="secondary" size="sm">
              Clear Filters
            </Button>
            <Button onClick={applyFilters} variant="primary" size="sm">
              Apply Filters
            </Button>
            {Object.keys(appliedFilters).length > 0 && (
              <RoleBasedComponent requiredPermission="canExport">
                <Button
                  onClick={exportToExcel}
                  variant="primary"
                  size="sm"
                  className="bg-blue-600 hover:bg-blue-700"
                  disabled={isExporting}
                >
                  {isExporting ? (
                    <>
                      <svg
                        className="animate-spin -ml-1 mr-2 h-4 w-4 text-white"
                        xmlns="http://www.w3.org/2000/svg"
                        fill="none"
                        viewBox="0 0 24 24"
                      >
                        <circle
                          className="opacity-25"
                          cx="12"
                          cy="12"
                          r="10"
                          stroke="currentColor"
                          strokeWidth="4"
                        ></circle>
                        <path
                          className="opacity-75"
                          fill="currentColor"
                          d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                        ></path>
                      </svg>
                      Exporting...
                    </>
                  ) : (
                    <>📊 Export to Excel</>
                  )}
                </Button>
              </RoleBasedComponent>
            )}
            <Button
              onClick={() => setIsReportModalOpen(true)}
              variant="primary"
              size="sm"
              className="bg-green-600 hover:bg-green-700"
            >
              📊 Summary Report
            </Button>
            {/* <Button
              onClick={handlePrintMockReport}
              variant="primary"
              size="sm"
              className="bg-purple-600 hover:bg-purple-700"
            >
              🖨️ Print Mock Report
            </Button> */}
          </div>
        </div>
      </div>

      {/* Display Controls */}
      {Object.keys(appliedFilters).length > 0 && (
        <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-4">
          <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
            <div className="flex items-center gap-4">
              <div className="flex items-center gap-2">
                <span className="text-sm font-medium text-gray-700">
                  📄 Rows per page:
                </span>
                <select
                  value={pageSize}
                  onChange={(e) =>
                    handlePageSizeChange(parseInt(e.target.value))
                  }
                  className="px-3 py-1 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm"
                >
                  <option value={10}>10</option>
                  <option value={20}>20</option>
                  <option value={50}>50</option>
                  <option value={100}>100</option>
                </select>
              </div>

              {totalRecords > 0 && (
                <div className="text-sm text-gray-600">
                  Showing {(currentPage - 1) * pageSize + 1} to{" "}
                  {Math.min(currentPage * pageSize, totalRecords)} of{" "}
                  {totalRecords} transactions
                </div>
              )}
            </div>

            <div className="flex items-center gap-2 text-sm text-gray-600">
              <span className="px-2 py-1 bg-blue-100 text-blue-800 rounded-md">
                {Object.keys(appliedFilters).length} filter
                {Object.keys(appliedFilters).length !== 1 ? "s" : ""} applied
              </span>
            </div>
          </div>
        </div>
      )}

      {/* Transaction Table */}
      <div className="bg-white rounded-xl shadow-sm border border-gray-200">
        {isLoading ? (
          <div className="flex justify-center py-12">
            <MinimalLoader />
          </div>
        ) : transactions.length === 0 ? (
          // Empty State - No Table
          <div className="px-6 py-12 text-center">
            <div className="flex flex-col items-center justify-center">
              <svg
                className="w-12 h-12 text-gray-400 mb-4"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
                />
              </svg>
              <h3 className="text-lg font-medium text-gray-900 mb-2">
                No transactions found
              </h3>
              {/* <p className="text-gray-500 mb-4">
                {Object.keys(appliedFilters).length > 0
                  ? "No transactions match your current filters. Try adjusting your search criteria."
                  : "Apply filters above to search for transactions, or click 'Refresh' to load all transactions."}
              </p> */}
            </div>
          </div>
        ) : (
          // Table with Data
          <div className="overflow-hidden">
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th
                      className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100 min-w-[250px]"
                      onClick={() => handleSort("transaction_time")}
                    >
                      <div className="flex items-center gap-1">
                        Transaction Time
                        {sortBy === "transaction_time" && (
                          <span className="text-blue-500">
                            {sortOrder === "ASC" ? "↑" : "↓"}
                          </span>
                        )}
                      </div>
                    </th>
                    <th
                      className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100 min-w-[250px]"
                      onClick={() => handleSort("transaction_id")}
                    >
                      <div className="flex items-center gap-1">
                        Transaction ID
                        {sortBy === "transaction_id" && (
                          <span className="text-blue-500">
                            {sortOrder === "ASC" ? "↑" : "↓"}
                          </span>
                        )}
                      </div>
                    </th>
                    <th
                      className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100 min-w-[250px]"
                      onClick={() => handleSort("transaction_merchant_name")}
                    >
                      <div className="flex items-center gap-1">
                        Merchant
                        {sortBy === "transaction_merchant_name" && (
                          <span className="text-blue-500">
                            {sortOrder === "ASC" ? "↑" : "↓"}
                          </span>
                        )}
                      </div>
                    </th>
                    <th
                      className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100 min-w-[250px]"
                      onClick={() => handleSort("transaction_merchant_vat")}
                    >
                      <div className="flex items-center gap-1">
                        VAT
                        {sortBy === "transaction_merchant_vat" && (
                          <span className="text-blue-500">
                            {sortOrder === "ASC" ? "↑" : "↓"}
                          </span>
                        )}
                      </div>
                    </th>
                    <th
                      className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100 min-w-[250px]"
                      onClick={() => handleSort("transaction_sub_mch_id")}
                    >
                      <div className="flex items-center gap-1">
                        TID
                        {sortBy === "transaction_sub_mch_id" && (
                          <span className="text-blue-500">
                            {sortOrder === "ASC" ? "↑" : "↓"}
                          </span>
                        )}
                      </div>
                    </th>
                    <th
                      className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100 min-w-[250px]"
                      onClick={() => handleSort("transaction_amount")}
                    >
                      <div className="flex items-center gap-1">
                        Amount
                        {sortBy === "transaction_amount" && (
                          <span className="text-blue-500">
                            {sortOrder === "ASC" ? "↑" : "↓"}
                          </span>
                        )}
                      </div>
                    </th>
                    <th
                      className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100 min-w-[250px]"
                      onClick={() => handleSort("transaction_trade_status")}
                    >
                      <div className="flex items-center gap-1">
                        Status
                        {sortBy === "transaction_trade_status" && (
                          <span className="text-blue-500">
                            {sortOrder === "ASC" ? "↑" : "↓"}
                          </span>
                        )}
                      </div>
                    </th>
                    <th
                      className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100 min-w-[250px]"
                      onClick={() => handleSort("transaction_channel_type")}
                    >
                      <div className="flex items-center gap-1">
                        Channel
                        {sortBy === "transaction_channel_type" && (
                          <span className="text-blue-500">
                            {sortOrder === "ASC" ? "↑" : "↓"}
                          </span>
                        )}
                      </div>
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider min-w-[250px]">
                      Actions
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {transactions.map((transaction) => (
                    <tr
                      key={transaction.id}
                      className="hover:bg-gray-50 transition-colors"
                    >
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 min-w-[250px]">
                        {formatDate(transaction.transaction_time)}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap min-w-[250px]">
                        <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800 font-mono">
                          {transaction.transaction_id}
                        </span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 min-w-[250px]">
                        <div className="font-medium">
                          {transaction.transaction_merchant_name || "N/A"}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 min-w-[250px]">
                        <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800 font-mono">
                          {transaction.transaction_merchant_vat || "N/A"}
                        </span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 min-w-[250px]">
                        <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800 font-mono">
                          {transaction.transaction_sub_mch_id || "N/A"}
                        </span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 min-w-[250px]">
                        <div
                          className={`font-medium ${
                            transaction.transaction_amount < 0
                              ? "text-red-600"
                              : "text-green-600"
                          }`}
                        >
                          {formatAmount(transaction.transaction_amount)}
                        </div>
                        {/* <div className="text-gray-500 text-xs">
                          {transaction.transaction_fee_type || "THB"}
                        </div> */}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap min-w-[250px]">
                        {getStatusBadge(
                          transaction.transaction_trade_status || "UNKNOWN"
                        )}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap min-w-[250px]">
                        {getChannelBadge(
                          transaction.transaction_channel_type || "UNKNOWN"
                        )}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium min-w-[300px]">
                        <div className="flex gap-2">
                          <Button
                            className="flex justify-center items-center"
                            onClick={() => handleView(transaction)}
                            variant="secondary"
                            size="sm"
                          >
                            <svg
                              className="w-4 h-4 mr-1"
                              fill="none"
                              stroke="currentColor"
                              viewBox="0 0 24 24"
                            >
                              <path
                                strokeLinecap="round"
                                strokeLinejoin="round"
                                strokeWidth={2}
                                d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"
                              />
                              <path
                                strokeLinecap="round"
                                strokeLinejoin="round"
                                strokeWidth={2}
                                d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"
                              />
                            </svg>
                            View
                          </Button>
                          {transaction.transaction_trade_status?.toUpperCase() === 'SUCCESS' && (
                            <>
                              <Button
                                className="flex justify-center items-center"
                                onClick={() => handleNewAdjustmentRequest(transaction)}
                                variant="primary"
                                size="sm"
                              >
                                <svg
                                  className="w-4 h-4 mr-1"
                                  fill="none"
                                  stroke="currentColor"
                                  viewBox="0 0 24 24"
                                >
                                  <path
                                    strokeLinecap="round"
                                    strokeLinejoin="round"
                                    strokeWidth={2}
                                    d="M12 6v6m0 0v6m0-6h6m-6 0H6"
                                  />
                                </svg>
                                Create Adjustment
                              </Button>
                              <Button
                                className="flex justify-center items-center"
                                onClick={() => handleAdjustmentRequest(transaction)}
                                variant="secondary"
                                size="sm"
                              >
                                <svg
                                  className="w-4 h-4 mr-1"
                                  fill="none"
                                  stroke="currentColor"
                                  viewBox="0 0 24 24"
                                >
                                  <path
                                    strokeLinecap="round"
                                    strokeLinejoin="round"
                                    strokeWidth={2}
                                    d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
                                  />
                                </svg>
                                PDF Form
                              </Button>
                            </>
                          )}
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>
        )}

        {/* Pagination Controls */}
        {!isLoading && totalPages > 1 && transactions.length > 0 && (
          <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6 mt-6">
            <div className="flex flex-col sm:flex-row items-center justify-between gap-4">
              <div className="text-sm text-gray-600">
                Showing{" "}
                <span className="font-medium">
                  {transactions.length > 0
                    ? (currentPage - 1) * pageSize + 1
                    : 0}
                </span>{" "}
                to{" "}
                <span className="font-medium">
                  {Math.min(currentPage * pageSize, totalRecords)}
                </span>{" "}
                of <span className="font-medium">{totalRecords}</span>{" "}
                transactions
              </div>

              <div className="flex items-center gap-2">
                <Button
                  onClick={() => handlePageChange(1)}
                  disabled={currentPage === 1}
                  variant="secondary"
                  size="sm"
                >
                  First
                </Button>
                <Button
                  onClick={() => handlePageChange(currentPage - 1)}
                  disabled={currentPage === 1}
                  variant="secondary"
                  size="sm"
                >
                  Previous
                </Button>

                <div className="flex items-center gap-1">
                  {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
                    let pageNum;
                    if (totalPages <= 5) {
                      pageNum = i + 1;
                    } else if (currentPage <= 3) {
                      pageNum = i + 1;
                    } else if (currentPage >= totalPages - 2) {
                      pageNum = totalPages - 4 + i;
                    } else {
                      pageNum = currentPage - 2 + i;
                    }

                    return (
                      <Button
                        key={pageNum}
                        onClick={() => handlePageChange(pageNum)}
                        variant={
                          currentPage === pageNum ? "primary" : "secondary"
                        }
                        size="sm"
                        className="min-w-[2.5rem]"
                      >
                        {pageNum}
                      </Button>
                    );
                  })}
                </div>

                <Button
                  onClick={() => handlePageChange(currentPage + 1)}
                  disabled={currentPage === totalPages}
                  variant="secondary"
                  size="sm"
                >
                  Next
                </Button>
                <Button
                  onClick={() => handlePageChange(totalPages)}
                  disabled={currentPage === totalPages}
                  variant="secondary"
                  size="sm"
                >
                  Last
                </Button>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Transaction Detail Modal */}
      <Modal isOpen={isModalOpen} onClose={handleCloseModal} size="lg">
        {viewingTransaction && (
          <div className="space-y-6">
            {/* Basic Information */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Transaction ID
                </label>
                <div className="px-3 py-2 bg-gray-50 border border-gray-300 rounded-md text-sm font-mono">
                  {viewingTransaction.transaction_id}
                </div>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Reference No
                </label>
                <div className="px-3 py-2 bg-gray-50 border border-gray-300 rounded-md text-sm">
                  {viewingTransaction.reference_no || "N/A"}
                </div>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Transaction Time
                </label>
                <div className="px-3 py-2 bg-gray-50 border border-gray-300 rounded-md text-sm">
                  {formatDate(viewingTransaction.transaction_time)}
                </div>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Amount
                </label>
                <div
                  className={`px-3 py-2 bg-gray-50 border border-gray-300 rounded-md text-sm font-medium ${
                    viewingTransaction.transaction_amount < 0
                      ? "text-red-600"
                      : "text-green-600"
                  }`}
                >
                  {formatAmount(viewingTransaction.transaction_amount)}{" "}
                  {viewingTransaction.transaction_fee_type || "THB"}
                </div>
              </div>
            </div>

            {/* Merchant Information */}
            <div>
              <h3 className="text-lg font-medium text-gray-900 mb-3">
                Merchant Information
              </h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Merchant Name
                  </label>
                  <div className="px-3 py-2 bg-gray-50 border border-gray-300 rounded-md text-sm">
                    {viewingTransaction.transaction_merchant_name || "N/A"}
                  </div>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Merchant VAT
                  </label>
                  <div className="px-3 py-2 bg-gray-50 border border-gray-300 rounded-md text-sm">
                    {viewingTransaction.transaction_merchant_vat || "N/A"}
                  </div>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Merchant ID
                  </label>
                  <div className="px-3 py-2 bg-gray-50 border border-gray-300 rounded-md text-sm">
                    {viewingTransaction.transaction_merchant_id || "N/A"}
                  </div>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Sub Merchant ID (TID)
                  </label>
                  <div className="px-3 py-2 bg-gray-50 border border-gray-300 rounded-md text-sm">
                    {viewingTransaction.transaction_sub_mch_id || "N/A"}
                  </div>
                </div>
              </div>
            </div>

            {/* Transaction Details */}
            <div>
              <h3 className="text-lg font-medium text-gray-900 mb-3">
                Transaction Details
              </h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Status
                  </label>
                  <div className="px-3 py-2 bg-gray-50 border border-gray-300 rounded-md text-sm">
                    {getStatusBadge(
                      viewingTransaction.transaction_trade_status || "UNKNOWN"
                    )}
                  </div>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Channel
                  </label>
                  <div className="px-3 py-2 bg-gray-50 border border-gray-300 rounded-md text-sm">
                    {getChannelBadge(
                      viewingTransaction.transaction_channel_type || "UNKNOWN"
                    )}
                  </div>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Trade Type
                  </label>
                  <div className="px-3 py-2 bg-gray-50 border border-gray-300 rounded-md text-sm">
                    {viewingTransaction.transaction_trade_type || "N/A"}
                  </div>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Bank Type
                  </label>
                  <div className="px-3 py-2 bg-gray-50 border border-gray-300 rounded-md text-sm">
                    {viewingTransaction.transaction_bank_type || "N/A"}
                  </div>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Card Number
                  </label>
                  <div className="px-3 py-2 bg-gray-50 border border-gray-300 rounded-md text-sm font-mono">
                    {viewingTransaction.transaction_card_no || "N/A"}
                  </div>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Coupon Amount
                  </label>
                  <div className="px-3 py-2 bg-gray-50 border border-gray-300 rounded-md text-sm">
                    {viewingTransaction.transaction_coupon_amount
                      ? formatAmount(
                          viewingTransaction.transaction_coupon_amount
                        )
                      : "0.00"}
                  </div>
                </div>
              </div>
            </div>

            {/* Modal Actions */}
            <div className="flex justify-end gap-3 pt-4 border-t border-gray-200">
              <Button onClick={handleCloseModal} variant="secondary">
                Close
              </Button>
            </div>
          </div>
        )}
      </Modal>

      {/* Summary Report Modal */}
      <ReportModal
        isOpen={isReportModalOpen}
        onClose={() => setIsReportModalOpen(false)}
        filters={appliedFilters}
      />

      {/* PDF Modal */}
      <Modal 
        isOpen={isPDFModalOpen} 
        onClose={() => setIsPDFModalOpen(false)} 
        size="lg"
      >
        <div className="space-y-6">
          <div className="flex items-center justify-between">
            <h3 className="text-lg font-medium text-gray-900">
              Dispute Resolution Form
            </h3>
            <button
              onClick={() => setIsPDFModalOpen(false)}
              className="text-gray-400 hover:text-gray-500"
            >
              <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>
          
          <div className="text-sm text-gray-600 mb-4">
            Generate and print the dispute resolution form with pre-filled sample data.
          </div>

          <DisputeFormPDF 
            onDownload={() => {
              showSuccessNotification('PDF downloaded successfully!');
            }}
            onPrint={() => {
              showSuccessNotification('PDF opened for printing!');
              setIsPDFModalOpen(false);
            }}
          />

          <div className="flex justify-end pt-4 border-t border-gray-200">
            <Button onClick={() => setIsPDFModalOpen(false)} variant="secondary">
              Close
            </Button>
          </div>
        </div>
      </Modal>

      {/* Form Adjustment Request Modal */}
      <Modal
        isOpen={isAdjustmentModalOpen}
        onClose={handleCloseAdjustmentModal}
        size="xl"
      >
        {adjustmentTransaction && (
          <div className="space-y-6">
            {/* Header */}
            <div className="flex items-center justify-between border-b border-gray-200 pb-4">
              <div className="flex items-center gap-2">
                <svg className="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                </svg>
                <h3 className="text-xl font-semibold text-gray-900">Form Adjustment Request</h3>
              </div>
              {/* <button
                onClick={handleCloseAdjustmentModal}
                className="text-gray-400 hover:text-gray-500"
              >
                <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button> */}
            </div>

            {/* Transaction Information Section */}
            <div className="bg-blue-50 p-4 rounded-lg">
              <h4 className="text-blue-800 font-semibold mb-3 border-b border-blue-200 pb-2">
                Transaction Information
              </h4>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Merchant ID <span className="text-red-500">*</span>
                  </label>
                  <div className="px-3 py-2 bg-gray-50 border border-gray-300 rounded-md text-sm">
                    {adjustmentTransaction.transaction_merchant_id || 'N/A'}
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Merchant Name <span className="text-red-500">*</span>
                  </label>
                  <div className="px-3 py-2 bg-gray-50 border border-gray-300 rounded-md text-sm">
                    {adjustmentTransaction.transaction_merchant_name || 'N/A'}
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Card Number <span className="text-red-500">*</span>
                  </label>
                  <div className="px-3 py-2 bg-gray-50 border border-gray-300 rounded-md text-sm">
                    {adjustmentTransaction.transaction_card_no || 'N/A'}
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    MDR
                  </label>
                  <div className="px-3 py-2 bg-gray-50 border border-gray-300 rounded-md text-sm">
                    0.90
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Transaction Date <span className="text-red-500">*</span>
                  </label>
                  <div className="px-3 py-2 bg-gray-50 border border-gray-300 rounded-md text-sm">
                    {formatDate(adjustmentTransaction.transaction_time)}
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Bank account
                  </label>
                  <div className="px-3 py-2 bg-gray-50 border border-gray-300 rounded-md text-sm">
                    ********** - 002
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Withholding Tax
                  </label>
                  <div className="px-3 py-2 bg-gray-50 border border-gray-300 rounded-md text-sm">
                    3.00
                  </div>
                </div>
              </div>

              {/* Second Row */}
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mt-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Currency Code
                  </label>
                  <div className="px-3 py-2 bg-gray-50 border border-gray-300 rounded-md text-sm">
                    THB
                  </div>
                </div>



                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Reference Number
                  </label>
                  <div className="px-3 py-2 bg-gray-50 border border-gray-300 rounded-md text-sm">
                    {adjustmentTransaction.reference_no || 'N/A'}
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Merchant Vat
                  </label>
                  <div className="px-3 py-2 bg-gray-50 border border-gray-300 rounded-md text-sm">
                    {adjustmentTransaction.transaction_merchant_vat || 'N/A'}
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Transaction Amount
                  </label>
                  <div className="px-3 py-2 bg-gray-50 border border-gray-300 rounded-md text-sm text-right">
                    {adjustmentTransaction.transaction_amount?.toLocaleString('th-TH', { minimumFractionDigits: 2 }) || '0.00'}
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Channel Type
                  </label>
                  <div className="px-3 py-2 bg-gray-50 border border-gray-300 rounded-md text-sm">
                    {adjustmentTransaction.transaction_channel_type || 'N/A'}
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Terminal ID
                  </label>
                  <div className="px-3 py-2 bg-gray-50 border border-gray-300 rounded-md text-sm">
                    33173516
                  </div>
                </div>
              </div>
            </div>

            {/* Dispute Information Section */}
            <div className="bg-blue-50 p-4 rounded-lg">
              <h4 className="text-blue-800 font-semibold mb-3 border-b border-blue-200 pb-2">
                Dispute Information
              </h4>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Disputed Amount
                  </label>
                  <input
                    type="text"
                    defaultValue="8,000.00"
                    className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm text-right"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Currency Code
                  </label>
                  <select className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm">
                    <option value="THB">THB</option>
                  </select>
                </div>
              </div>

              {/* Radio Button Groups */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mt-4">
                <div>
                  <div className="space-y-2">
                    <div className="flex items-center">
                      <input
                        id="full-amount"
                        name="amount-type"
                        type="radio"
                        defaultChecked
                        className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300"
                      />
                      <label htmlFor="full-amount" className="ml-2 text-sm text-gray-700">
                        Full Amount
                      </label>
                    </div>
                    <div className="flex items-center">
                      <input
                        id="partial-amount"
                        name="amount-type"
                        type="radio"
                        className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300"
                      />
                      <label htmlFor="partial-amount" className="ml-2 text-sm text-gray-700">
                        Partial Amount
                      </label>
                    </div>
                  </div>

                  <div className="mt-4">
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Dispute Resolution Type
                    </label>
                    <div className="space-y-2">
                      <div className="flex items-center">
                        <input
                          id="credit-adjustment"
                          name="resolution-type"
                          type="radio"
                          defaultChecked
                          className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300"
                        />
                        <label htmlFor="credit-adjustment" className="ml-2 text-sm text-gray-700">
                          Credit Adjustment
                        </label>
                      </div>
                      <div className="flex items-center">
                        <input
                          id="first-chargeback"
                          name="resolution-type"
                          type="radio"
                          className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300"
                        />
                        <label htmlFor="first-chargeback" className="ml-2 text-sm text-gray-700">
                          First Chargeback
                        </label>
                      </div>
                      <div className="flex items-center">
                        <input
                          id="debit-adjustment"
                          name="resolution-type"
                          type="radio"
                          className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300"
                        />
                        <label htmlFor="debit-adjustment" className="ml-2 text-sm text-gray-700">
                          Debit Adjustment (Credit Card)
                        </label>
                      </div>
                      <div className="flex items-center">
                        <input
                          id="representment"
                          name="resolution-type"
                          type="radio"
                          className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300"
                        />
                        <label htmlFor="representment" className="ml-2 text-sm text-gray-700">
                          Representment (Credit Card)
                        </label>
                      </div>
                      <div className="flex items-center">
                        <input
                          id="second-chargeback"
                          name="resolution-type"
                          type="radio"
                          className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300"
                        />
                        <label htmlFor="second-chargeback" className="ml-2 text-sm text-gray-700">
                          Second Chargeback (Credit Card Non-ATM)
                        </label>
                      </div>
                    </div>
                  </div>

                  <div className="mt-4">
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Supporting Documents
                    </label>
                    <div className="space-y-2">
                      <div className="flex items-center">
                        <input
                          id="supporting-docs-yes"
                          name="supporting-documents"
                          type="radio"
                          defaultChecked
                          className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300"
                        />
                        <label htmlFor="supporting-docs-yes" className="ml-2 text-sm text-gray-700">
                          Yes
                        </label>
                      </div>
                      <div className="flex items-center">
                        <input
                          id="supporting-docs-no"
                          name="supporting-documents"
                          type="radio"
                          className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300"
                        />
                        <label htmlFor="supporting-docs-no" className="ml-2 text-sm text-gray-700">
                          No
                        </label>
                      </div>
                    </div>
                  </div>
                </div>

                <div>
                  <div className="space-y-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        CDRS
                      </label>
                      <div className="space-y-2">
                        <div className="flex items-center">
                          <input
                            id="cdrs-normal"
                            name="cdrs"
                            type="radio"
                            defaultChecked
                            className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300"
                          />
                          <label htmlFor="cdrs-normal" className="ml-2 text-sm text-blue-600">
                            Normal
                          </label>
                        </div>
                        <div className="flex items-center">
                          <input
                            id="cdrs-show-only"
                            name="cdrs"
                            type="radio"
                            className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300"
                          />
                          <label htmlFor="cdrs-show-only" className="ml-2 text-sm text-gray-700">
                            Show only
                          </label>
                        </div>
                        <div className="flex items-center">
                          <input
                            id="cdrs-do-not-show"
                            name="cdrs"
                            type="radio"
                            className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300"
                          />
                          <label htmlFor="cdrs-do-not-show" className="ml-2 text-sm text-gray-700">
                            Do not show
                          </label>
                        </div>
                      </div>
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Card Type
                      </label>
                      <textarea
                        rows={2}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm"
                      />
                    </div>
                  </div>
                </div>
              </div>

              {/* Additional Message and CDRS Section */}
              <div className="grid grid-cols-1 gap-6 mt-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Add nation Message
                  </label>
                  <textarea
                    rows={4}
                    defaultValue="Refund diff amount to customer."
                    className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm"
                  />
                </div>

                
              </div>
            </div>

            {/* Action Buttons */}
            <div className="flex justify-center gap-4 pt-6 border-t border-gray-200">
             

              <Button
                variant="primary"
                className="flex items-center gap-2 px-6 py-3"
                onClick={() => {
                  showSuccessNotification('Form updated successfully!');
                }}
              >
                <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                </svg>
                บันทึก (Save)
              </Button>
              <Button
                variant="secondary"
                className="flex items-center gap-2 px-6 py-3"
                onClick={handleDownloadAdjustmentForm}
              >
                <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                </svg>
                ดาวน์โหลด (Download)
              </Button>

              <Button
                variant="primary"
                className="flex items-center gap-2 px-6 py-3"
                onClick={handlePrintAdjustmentForm}
              >
                <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 17h2a2 2 0 002-2v-4a2 2 0 00-2-2H5a2 2 0 00-2 2v4a2 2 0 002 2h2m2 4h6a2 2 0 002-2v-4a2 2 0 00-2-2H9a2 2 0 00-2 2v4a2 2 0 002 2zm8-12V5a2 2 0 00-2-2H9a2 2 0 00-2 2v4h10z" />
                </svg>
                พิมพ์ (Print)
              </Button>
            </div>
          </div>
        )}
      </Modal>
    </div>
  );
}
