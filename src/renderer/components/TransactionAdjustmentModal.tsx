import React, { useState, useEffect } from 'react';
import { Modal } from './modal';
import { Button } from './button';
import { useAuth } from '../contexts/AuthContext';
import { useNotification } from '../contexts/NotificationContext';
import { safeIpcInvoke } from '../utils/electron';

interface Transaction {
  id: number;
  transaction_id: string;
  transaction_amount: number;
  transaction_merchant_id?: string;
  transaction_merchant_vat?: string;
  transaction_merchant_name?: string;
  transaction_card_no?: string;
  transaction_trade_type?: string;
  transaction_channel_type?: string;
}

interface TransactionAdjustmentModalProps {
  isOpen: boolean;
  onClose: () => void;
  transaction: Transaction | null;
  onSuccess?: () => void;
}

interface AdjustmentFormData {
  disputed_amount: number;
  disputed_full: number;
  disputed_type: string;
  currency_code: string;
  support_document: boolean;
  cdrs: string;
  card_type: string;
  reason: string;
}

export function TransactionAdjustmentModal({
  isOpen,
  onClose,
  transaction,
  onSuccess
}: TransactionAdjustmentModalProps) {
  const { user } = useAuth();
  const { showNotification } = useNotification();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [formData, setFormData] = useState<AdjustmentFormData>({
    disputed_amount: 0,
    disputed_full: 0,
    disputed_type: '',
    currency_code: 'THB',
    support_document: false,
    cdrs: '',
    card_type: '',
    reason: 'Refund diff amount to customer.'
  });

  // Pre-populate form when transaction changes
  useEffect(() => {
    if (transaction) {
      setFormData(prev => ({
        ...prev,
        disputed_amount: transaction.transaction_amount,
        card_type: transaction.transaction_card_no || '',
        reason: 'Refund diff amount to customer.'
      }));
    }
  }, [transaction]);

  const handleInputChange = (field: keyof AdjustmentFormData, value: any) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!transaction || !user) {
      showNotification('Missing transaction or user information', 'error');
      return;
    }

    if (formData.disputed_amount <= 0) {
      showNotification('Disputed amount must be greater than 0', 'error');
      return;
    }

    setIsSubmitting(true);

    try {
      const adjustmentData = {
        disputed_amount: formData.disputed_amount,
        disputed_full: formData.disputed_full,
        disputed_type: formData.disputed_type || null,
        currency_code: formData.currency_code,
        support_document: formData.support_document,
        cdrs: formData.cdrs || null,
        card_type: formData.card_type || null,
        reason: formData.reason,
        transaction_ref: transaction.transaction_id,
        transaction_id: transaction.transaction_id,
        merchant_vat: transaction.transaction_merchant_vat,
        merchant_id: transaction.transaction_merchant_id,
        create_by: user.user_name
      };

      const result = await safeIpcInvoke('create-transaction-adjustment-request', adjustmentData);

      if (result.success) {
        showNotification('Transaction adjustment request created successfully', 'success');
        onSuccess?.();
        onClose();
        
        // Reset form
        setFormData({
          disputed_amount: 0,
          disputed_full: 0,
          disputed_type: '',
          currency_code: 'THB',
          support_document: false,
          cdrs: '',
          card_type: '',
          reason: 'Refund diff amount to customer.'
        });
      } else {
        showNotification(result.message || 'Failed to create adjustment request', 'error');
      }
    } catch (error) {
      console.error('Error creating adjustment request:', error);
      showNotification('An error occurred while creating the adjustment request', 'error');
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleClose = () => {
    if (!isSubmitting) {
      onClose();
    }
  };

  if (!transaction) {
    return null;
  }

  return (
    <Modal isOpen={isOpen} onClose={handleClose} size="lg">
      <div className="space-y-6">
        {/* Header */}
        <div className="border-b border-gray-200 pb-4">
          <h2 className="text-xl font-semibold text-gray-900">
            Transaction Adjustment Request
          </h2>
          <p className="text-sm text-gray-600 mt-1">
            Create an adjustment request for transaction: {transaction.transaction_id}
          </p>
        </div>

        {/* Transaction Info */}
        <div className="bg-gray-50 rounded-lg p-4">
          <h3 className="text-sm font-medium text-gray-900 mb-3">Transaction Information</h3>
          <div className="grid grid-cols-2 gap-4 text-sm">
            <div>
              <span className="text-gray-600">Transaction ID:</span>
              <div className="font-mono">{transaction.transaction_id}</div>
            </div>
            <div>
              <span className="text-gray-600">Original Amount:</span>
              <div className="font-semibold">฿{transaction.transaction_amount.toLocaleString()}</div>
            </div>
            <div>
              <span className="text-gray-600">Merchant:</span>
              <div>{transaction.transaction_merchant_name || 'N/A'}</div>
            </div>
            <div>
              <span className="text-gray-600">Channel:</span>
              <div>{transaction.transaction_channel_type || 'N/A'}</div>
            </div>
          </div>
        </div>

        {/* Form */}
        <form onSubmit={handleSubmit} className="space-y-4">
          {/* Disputed Amount */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Disputed Amount <span className="text-red-500">*</span>
            </label>
            <input
              type="number"
              step="0.01"
              min="0"
              value={formData.disputed_amount}
              onChange={(e) => handleInputChange('disputed_amount', parseFloat(e.target.value) || 0)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              required
            />
          </div>

          {/* Disputed Type */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Disputed Type
            </label>
            <select
              value={formData.disputed_type}
              onChange={(e) => handleInputChange('disputed_type', e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="">Select type</option>
              <option value="refund">Refund</option>
              <option value="chargeback">Chargeback</option>
              <option value="adjustment">Adjustment</option>
              <option value="other">Other</option>
            </select>
          </div>

          {/* Currency Code */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Currency Code
            </label>
            <select
              value={formData.currency_code}
              onChange={(e) => handleInputChange('currency_code', e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="THB">THB</option>
              <option value="USD">USD</option>
              <option value="EUR">EUR</option>
              <option value="CNY">CNY</option>
            </select>
          </div>

          {/* Support Document */}
          <div className="flex items-center">
            <input
              type="checkbox"
              id="support_document"
              checked={formData.support_document}
              onChange={(e) => handleInputChange('support_document', e.target.checked)}
              className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
            />
            <label htmlFor="support_document" className="ml-2 block text-sm text-gray-700">
              Support Document Provided
            </label>
          </div>

          {/* CDRS */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              CDRS
            </label>
            <input
              type="text"
              value={formData.cdrs}
              onChange={(e) => handleInputChange('cdrs', e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              maxLength={20}
            />
          </div>

          {/* Additional Message (Reason) */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Additional Message
            </label>
            <textarea
              value={formData.reason}
              onChange={(e) => handleInputChange('reason', e.target.value)}
              rows={3}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              placeholder="Refund diff amount to customer."
            />
          </div>

          {/* Action Buttons */}
          <div className="flex justify-end space-x-3 pt-4 border-t border-gray-200">
            <Button
              type="button"
              variant="secondary"
              onClick={handleClose}
              disabled={isSubmitting}
            >
              Cancel
            </Button>
            <Button
              type="submit"
              variant="primary"
              disabled={isSubmitting}
              loading={isSubmitting}
            >
              {isSubmitting ? 'Creating...' : 'Create Adjustment Request'}
            </Button>
          </div>
        </form>
      </div>
    </Modal>
  );
}
