-- Migration: Create transaction_request_adjust table
-- Description: Create table for transaction adjustment requests with financial calculation relationships
-- Date: 2025-08-07

-- Create transaction_request_adjust table
CREATE TABLE IF NOT EXISTS transaction_request_adjust (
    id SERIAL PRIMARY KEY,
    disputed_amount DECIMAL(10,2),
    disputed_full INTEGER,
    disputed_type VA<PERSON>HA<PERSON>(50),
    currency_code CHAR(3),
    support_document BOOLEAN DEFAULT FALSE,
    cdrs VARCHAR(20),
    card_type TEXT,
    reason TEXT,
    transaction_ref VARCHAR(255),
    transaction_report_detail_id INTEGER REFERENCES transaction_summary_report_detail(id),
    
    -- Standard tracking columns
    active BOOLEAN NOT NULL DEFAULT TRUE,
    create_by VARCHAR(100) NOT NULL,
    create_dt TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    update_by VARCHAR(100),
    update_dt TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_transaction_request_adjust_transaction_ref ON transaction_request_adjust(transaction_ref);
CREATE INDEX IF NOT EXISTS idx_transaction_request_adjust_report_detail_id ON transaction_request_adjust(transaction_report_detail_id);
CREATE INDEX IF NOT EXISTS idx_transaction_request_adjust_create_dt ON transaction_request_adjust(create_dt);
CREATE INDEX IF NOT EXISTS idx_transaction_request_adjust_active ON transaction_request_adjust(active);

-- Add trigger to automatically update update_dt on record changes
CREATE OR REPLACE FUNCTION update_transaction_request_adjust_timestamp()
RETURNS TRIGGER AS $$
BEGIN
    NEW.update_dt = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_update_transaction_request_adjust_timestamp
    BEFORE UPDATE ON transaction_request_adjust
    FOR EACH ROW
    EXECUTE FUNCTION update_transaction_request_adjust_timestamp();

-- Add constraints
ALTER TABLE transaction_request_adjust 
ADD CONSTRAINT chk_disputed_amount_valid 
CHECK (disputed_amount >= 0);

ALTER TABLE transaction_request_adjust 
ADD CONSTRAINT chk_currency_code_valid 
CHECK (currency_code IS NULL OR LENGTH(currency_code) = 3);

-- Add comments for documentation
COMMENT ON TABLE transaction_request_adjust IS 'Table for storing transaction adjustment requests with financial calculation relationships';
COMMENT ON COLUMN transaction_request_adjust.disputed_amount IS 'Amount being disputed/adjusted';
COMMENT ON COLUMN transaction_request_adjust.disputed_full IS 'Flag indicating if full amount is disputed (1) or partial (0)';
COMMENT ON COLUMN transaction_request_adjust.disputed_type IS 'Type of dispute/adjustment';
COMMENT ON COLUMN transaction_request_adjust.currency_code IS 'Currency code (3-letter ISO code)';
COMMENT ON COLUMN transaction_request_adjust.support_document IS 'Flag indicating if supporting documents are provided';
COMMENT ON COLUMN transaction_request_adjust.cdrs IS 'CDRS reference number';
COMMENT ON COLUMN transaction_request_adjust.card_type IS 'Type of card used in the transaction';
COMMENT ON COLUMN transaction_request_adjust.reason IS 'Reason for the adjustment request';
COMMENT ON COLUMN transaction_request_adjust.transaction_ref IS 'Reference to the original transaction';
COMMENT ON COLUMN transaction_request_adjust.transaction_report_detail_id IS 'Foreign key to transaction_summary_report_detail for financial calculations';

-- Grant permissions (adjust as needed for your environment)
-- GRANT SELECT, INSERT, UPDATE, DELETE ON transaction_request_adjust TO your_app_user;
-- GRANT USAGE, SELECT ON SEQUENCE transaction_request_adjust_id_seq TO your_app_user;

-- Verify table creation
SELECT 
    table_name, 
    column_name, 
    data_type, 
    is_nullable,
    column_default
FROM information_schema.columns 
WHERE table_name = 'transaction_request_adjust' 
ORDER BY ordinal_position;
